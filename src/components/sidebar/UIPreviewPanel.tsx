import { RefreshCcw, Loader2 } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import AgentSleepingSVG from "@/assets/agentsleeping.svg";
import AgentBell from "@/assets/fluent-emoji_bell.svg";
import AlertFillSVG from "@/assets/alert-fill.svg";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import CloseSVG from "@/assets/panels/close.svg"
import LinkSVG from "@/assets/panels/link.svg"
import ShareSVG from "@/assets/panels/share.svg"
import RefreshSvg from "@/assets/panels/refresh.svg"
import { ShareModal } from "@/components/sidebar/ShareModal"

interface PortMapping {
  service: string;
  hostPort: number;
  containerPort: number;
}

interface UrlPreviewPanelProps {
  isOpen: boolean;
  onClose: () => void;
  previewUrl: string;
  shareableLink: string;
  portMappings?: PortMapping[];
  podIsPaused?: boolean;
  showCase?: boolean;
  onResumePod?: () => void;
  isFromGithub?: any;
  shouldReloadIframe?: boolean;
  onIframeReloaded?: () => void;
  isResizing?: boolean;
}

export function UrlPreviewPanel({
  isOpen,
  onClose,
  previewUrl,
  shareableLink,
  showCase,
  podIsPaused = false,
  onResumePod,
  isFromGithub,
  shouldReloadIframe = false,
  onIframeReloaded,
  isResizing = false,
}: UrlPreviewPanelProps) {
  const [iframeLoading, setIframeLoading] = useState(true);
  const [loadingFailed, setLoadingFailed] = useState(false);
  const [loadingErrorMessage, setLoadingErrorMessage] = useState(
    "The preview could not be loaded. Please try again.",
  );
  const [showShareModal, setShowShareModal] = useState(false);
  const [isCheckingHealth, setIsCheckingHealth] = useState(false);
  const [healthCheckAttempt, setHealthCheckAttempt] = useState(0);
  const [showIframe, setShowIframe] = useState(false);

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [key, setKey] = useState(0);
  const healthCheckAbortRef = useRef<AbortController | null>(null);
  const iframeDelayTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isHealthCheckRunningRef = useRef(false);

  const MAX_HEALTH_CHECK_ATTEMPTS = 5;
  const HEALTH_CHECK_DELAY_MS = 1000; // 1 second delay between attempts (after previous completes)
  const IFRAME_DELAY_MS = 1000; // 1 second delay after successful health check

  // Handle iframe reload when URL or panel state changes
  useEffect(() => {
    if (isOpen && previewUrl && !podIsPaused) {
      handleRefresh();
    }

    return () => {
      clearAllTimers();
    };
  }, [isOpen, previewUrl, podIsPaused]);

  // Handle iframe reload when requested (after wakeup)
  useEffect(() => {
    if (shouldReloadIframe && previewUrl && !podIsPaused) {
      handleRefresh();

      // Notify parent that reload has been initiated
      if (onIframeReloaded) {
        onIframeReloaded();
      }
    }
  }, [shouldReloadIframe, previewUrl, podIsPaused]);

  // Clear all timers and abort ongoing requests to prevent memory leaks
  const clearAllTimers = () => {
    if (healthCheckAbortRef.current) {
      healthCheckAbortRef.current.abort();
      healthCheckAbortRef.current = null;
    }

    if (iframeDelayTimeoutRef.current) {
      clearTimeout(iframeDelayTimeoutRef.current);
      iframeDelayTimeoutRef.current = null;
    }

    isHealthCheckRunningRef.current = false;
  };

  const handleOpenInNewTab = () => {
    if (shareableLink || previewUrl) {
      window.open(showCase ? previewUrl : shareableLink, "_blank");
    }
  };

  const handleShareClick = () => {
    setShowShareModal(true);
  };

  // Health check function to verify if the URL is ready
  const performHealthCheck = async (url: string, attempt: number, abortController: AbortController): Promise<boolean> => {
    try {
      console.log(`Starting health check attempt ${attempt}/${MAX_HEALTH_CHECK_ATTEMPTS}`);

      const response = await fetch(url, {
        method: 'GET',
        mode: 'no-cors',
        cache: 'no-cache',
        signal: abortController.signal
      });

      console.log(`Health check attempt ${attempt} completed successfully`);

      // For no-cors mode, we can't check the actual status
      // But if the request doesn't throw an error, the service is likely up
      return true;
    } catch (error : any) {
      if (error.name === 'AbortError') {
        console.log(`Health check attempt ${attempt} was aborted`);
        return false;
      }

      console.log(`Health check attempt ${attempt} failed:`, error);
      return false;
    }
  };

  // Start health check process with sequential requests
  const startHealthCheck = async () => {
    if (!previewUrl || isHealthCheckRunningRef.current) return;

    // Clear any existing health check
    clearAllTimers();

    // Create new abort controller for this health check session
    healthCheckAbortRef.current = new AbortController();
    isHealthCheckRunningRef.current = true;

    setIsCheckingHealth(true);
    setHealthCheckAttempt(1);
    setShowIframe(false);
    setLoadingFailed(false);
    setIframeLoading(true);

    let currentAttempt = 1;

    const performSequentialHealthChecks = async () => {
      while (currentAttempt <= MAX_HEALTH_CHECK_ATTEMPTS && isHealthCheckRunningRef.current) {
        // Update the attempt number in state
        setHealthCheckAttempt(currentAttempt);

        const isHealthy = await performHealthCheck(
          previewUrl,
          currentAttempt,
          healthCheckAbortRef.current!
        );

        // Check if we were aborted during the request
        if (!isHealthCheckRunningRef.current) {
          return;
        }

        if (isHealthy) {
          console.log('Health check successful, preparing to show iframe');
          setIsCheckingHealth(false);
          isHealthCheckRunningRef.current = false;

          // Wait 1 second before showing the iframe
          iframeDelayTimeoutRef.current = setTimeout(() => {
            if (isHealthCheckRunningRef.current === false) { // Double check we haven't been aborted
              setShowIframe(true);
              setKey((prevKey) => prevKey + 1); // Force iframe recreation
            }
          }, IFRAME_DELAY_MS);

          return;
        }

        // If this was the last attempt, show error
        if (currentAttempt >= MAX_HEALTH_CHECK_ATTEMPTS) {
          console.log('All health check attempts failed');
          setIsCheckingHealth(false);
          setLoadingFailed(true);
          setIframeLoading(false);
          setLoadingErrorMessage(
            `Service is not responding after ${MAX_HEALTH_CHECK_ATTEMPTS} attempts. The service might be starting up or experiencing issues.`
          );
          isHealthCheckRunningRef.current = false;
          return;
        }

        // Wait before next attempt (only if we're still running)
        if (isHealthCheckRunningRef.current) {
          console.log(`Waiting ${HEALTH_CHECK_DELAY_MS}ms before next attempt...`);
          await new Promise(resolve => setTimeout(resolve, HEALTH_CHECK_DELAY_MS));
        }

        currentAttempt++;
      }
    };

    await performSequentialHealthChecks();
  };

  const handleRefresh = () => {
    if (!previewUrl) return;

    console.log("Starting health check for URL:", previewUrl);

    // Reset all states
    setIframeLoading(true);
    setLoadingFailed(false);
    setShowIframe(false);
    setLoadingErrorMessage("The preview could not be loaded. Please try again.");

    clearAllTimers();
    startHealthCheck();
  };

  return (
    <div
      className={cn(`w-full h-full bg-[#111112]  max-md:absolute max-md:inset-0`, isOpen ? "block" : "hidden")}
    >
      <div className="flex flex-col h-full">
        <div className="p-4 md:px-4 bg-[#111112] md:bg-transparent md:py-4 flex items-center justify-between border-b border-[#242424]/60">
          <div className="text-[#939399] font-['Brockmann']  text-[15px]  md:text-[18px] font-medium leading-[24px]">
            Preview
          </div>
          <div className="flex items-center gap-2">
            <TooltipProvider>
              {previewUrl && (
                <button
                  type="button"
                  onClick={handleShareClick}
                  className="flex items-center justify-center h-8 bg-white hover:bg-white/90 rounded-[6px] gap-1 p-1 pr-2"
                >
                  <img src={ShareSVG} alt="Share app" className="w-5 h-5" />
                  <span className="text-[#0F0F10] font-semibold text-[14px]">Share</span>
                </button>
              )}
              {previewUrl && (
                <Tooltip delayDuration={100}>
                  <TooltipTrigger asChild>
                    <button
                      type="button"
                      onClick={handleOpenInNewTab}
                      className="w-8 h-8 bg-[#FFFFFF0A] hover:bg-[#FFFFFF14] flex items-center justify-center rounded-[6px]"
                    >
                      <img src={LinkSVG} alt="Open in new tab" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="bg-[#DDDDE6] text-black border-0"
                    style={{ fontFamily: "Inter", fontWeight: 500 }}
                  >
                    Open in new tab
                  </TooltipContent>
                </Tooltip>
              )}
              {previewUrl && (
                <Tooltip delayDuration={100}>
                  <TooltipTrigger asChild>
                    <button
                      type="button"
                      onClick={handleRefresh}
                      className="w-8 h-8 bg-[#FFFFFF0A] hover:bg-[#FFFFFF14] flex items-center justify-center rounded-[6px]"
                    >
                      <img src={RefreshSvg} alt="Refresh" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="bg-[#DDDDE6] text-black border-0"
                    style={{ fontFamily: "Inter", fontWeight: 500 }}
                  >
                    Refresh
                  </TooltipContent>
                </Tooltip>
              )}
              <Tooltip delayDuration={100}>
                <TooltipTrigger asChild>
                  <button
                    type="button"
                    onClick={onClose}
                    className="w-8 h-8 bg-[#FFFFFF0A] hover:bg-[#FFFFFF14] flex items-center justify-center rounded-[6px]"
                  >
                    <img src={CloseSVG} alt="Close" className="w-5 h-5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent
                  side="bottom"
                  className="bg-[#DDDDE6] text-black border-0"
                  style={{ fontFamily: "Inter", fontWeight: 500 }}
                >
                  Close
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <div className="flex-1 p-0 overflow-hidden">
          {podIsPaused && !showCase ? (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <div className="w-full z-[20] px-[2rem] gap-6 rounded-[1rem] rounded-bl-none rounded-br-none pt-[2rem] pb-[3rem] flex flex-col justify-between items-center">
                <div className="flex flex-col items-center gap-5">
                  <img
                    src={AgentSleepingSVG}
                    className="min-w-[100px] min-h-[100px]"
                    alt="Agent Sleeping"
                  />
                  <div className="flex flex-col items-center gap-3">
                    <span className="text-[#C4C4CC] text-[20px]">
                      Preview paused - Agent is sleeping
                    </span>
                    {/* Show different messages based on GitHub status and creation date */}
                    {isFromGithub ? (
                      <span className="text-[#7b7b80] text-sm">
                        Apologies, the task has expired due to inactivity. We
                        may not be able to recover this at this moment. Please
                        start a new task using the same github repo. Reach out
                        to us at{" "}
                        <a
                          href="mailto:<EMAIL>"
                          className="text-[#ACACB2] font-extrabold underline underline-offset-1"
                        >
                          <EMAIL>
                        </a>{" "}
                        for any assistance.
                      </span>
                    ) : (
                      <span className="text-[#7b7b80] text-sm">
                        If you are having trouble accessing your work, Please
                        contact support at{" "}
                        <a
                          href="mailto:<EMAIL>"
                          className="bg-gradient-to-r font-['Inter'] from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text font-semibold underline underline-offset-1"
                        >
                          <EMAIL>
                        </a>
                      </span>
                    )}
                  </div>
                </div>
                {isFromGithub ? null : (
                  <button
                    type="button"
                    onClick={onResumePod}
                    className="bg-[#FCB94920] p-[10px] rounded-[6px] flex items-center gap-2 md:min-w-[200px] hover:bg-[#FCB94930] transition-colors duration-200"
                  >
                    <img src={AgentBell} alt="Wake icon" className="w-5 h-5" />
                    <span className="bg-gradient-to-r from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text font-medium">
                      Wake up the Agent
                    </span>
                  </button>
                )}
              </div>
            </div>
          ) : previewUrl && isOpen ? (
            <div className="relative w-full h-full">
              {/* Health Check Loading State */}
              {isCheckingHealth && (
                <div className="absolute inset-0 flex items-center justify-center bg-[#0F0F10]">
                  <div className="flex flex-col items-center">
                    <Loader2 className="w-8 h-8 text-[#5FD3F3] animate-spin mb-4" />
                    <p className="text-[#939399]">Loading preview...</p>
                  </div>
                </div>
              )}

              {/* Iframe Loading State */}
              {showIframe && iframeLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-[#0F0F10]">
                  <div className="flex flex-col items-center">
                    <Loader2 className="w-8 h-8 text-[#5FD3F3] animate-spin mb-4" />
                    <p className="text-[#939399]">Loading preview...</p>
                  </div>
                </div>
              )}

              {/* Error State */}
              {loadingFailed && (
                <div className="absolute inset-0 flex items-center justify-center bg-[#0F0F10]">
                  <div className="flex flex-col items-center max-w-md gap-6 text-center">
                    <img src={AlertFillSVG} alt="Alert" className="w-12 h-12" />
                    <div className="flex flex-col gap-2">
                      <p className="text-[#C4C4CC] text-[24px] font-medium">
                        Preview failed to load
                      </p>
                      <p className="text-[#939399]">{loadingErrorMessage}</p>
                    </div>
                    <button
                      type="button"
                      onClick={handleRefresh}
                      className="bg-[#FCB94920] p-[10px] rounded-[6px] flex items-center gap-2 hover:bg-[#FCB94930] transition-colors duration-200"
                    >
                      <RefreshCcw className="w-5 h-5 text-[#FCB949]" />
                      <span className="bg-gradient-to-r from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text font-medium">
                        Retry Health Check
                      </span>
                    </button>
                  </div>
                </div>
              )}

              {/* Iframe */}
              {showIframe && (
                <iframe
                  ref={iframeRef}
                  key={key}
                  src={previewUrl}
                  className="w-full h-full border-0"
                  style={{ pointerEvents: isResizing ? 'none' : 'auto' }}
                  onLoad={() => {
                    console.log("Iframe loaded successfully");
                    setIframeLoading(false);
                  }}
                  onError={(e) => {
                    console.error("Iframe failed to load:", e);
                    setIframeLoading(false);
                    setLoadingFailed(true);
                    setLoadingErrorMessage(
                      "Failed to load the preview after successful health check. Please try refreshing."
                    );
                  }}
                  title="URL Preview"
                  sandbox="allow-scripts allow-same-origin allow-forms allow-top-navigation allow-popups"
                />
              )}
            </div>
          ) : null}
        </div>
      </div>

      {/* Share Modal */}
      <ShareModal
        isOpen={showShareModal}
        onOpenChange={setShowShareModal}
        shareableLink={showCase ? previewUrl : shareableLink}
        fromShowcase={showCase}
      />
    </div>
  );
}