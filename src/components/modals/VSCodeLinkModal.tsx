import React from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import CopyButton from "../CopyButton";
import { X } from "lucide-react";

interface VSCodeLinkModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  vscodeUrl: string;
  vscodePassword: string;
  onOpenInBrowser: () => void;
}

export const VSCodeLinkModal: React.FC<VSCodeLinkModalProps> = ({
  isOpen,
  onOpenChange,
  vscodeUrl,
  vscodePassword,
  onOpenInBrowser,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="m-4 mx-auto max-w-[calc(100vw-32px)] sm:max-w-md">
        <div className="flex flex-col gap-2 p-4 md:p-6">
          <span className="text-[#FFFFFF] text-[22px] font-semibold">VS Code Link</span>
          <span className="text-[#FFFFFF70] text-[16px] font-medium">
            Access VS Code in your browser with the following link and password
          </span>

          <div className="absolute right-5 top-5 md:right-6 md:top-6 cursor-pointer bg-[#ffffff06] hover:bg-[#ffffff14] p-1 backdrop-blur-sm rounded-[8px]">
            <X
              className=" w-5 h-5 md:h-6 md:w-6 flex items-center justify-center text-[#7B7B80]"
              onClick={() => onOpenChange(false)}
            />
          </div>
        </div>

        <div className="relative p-4 space-y-4 md:pt-0 md:p-6">
          <div className="w-full space-y-2">
            <p className="text-sm font-medium text-[#737780]">Link</p>
            <div className="flex items-center w-full gap-2">
              <input
                type="text"
                aria-label="Link"
                className="flex-1 w-full px-3 py-2 bg-[#1A1A1B] border border-[#242424] rounded-md text-[#DDDDE6] text-sm"
                value={vscodeUrl}
                readOnly
              />
              <CopyButton
                  showIcon={true}
                  iconOnly={true}
                  className="border-none bg-none"
                  buttonClassName={buttonVariants({ variant: "outline", size: "sm" , className: "whitespace-nowrap rounded-lg  transition-all ease-in-out duration-200 bg-white/10 border-white/20 border hover:bg-white/20 hover:text-white text-white/40"})}
                  value={vscodeUrl}
                  tooltipText="Copy"
                  copiedTooltipText="Copied"
                  feedbackType="tooltip"
                  iconProps={{ size: 16 }}
                  onCopy={() => { }}
                />
            </div>
          </div>
          {vscodePassword && (
            <div className="w-full space-y-2">
              <p className="text-sm font-medium text-[#737780]">Password</p>
              <div className="flex items-center w-full gap-2">
                <input
                  
                  type="password"
                  aria-label="Password"
                  className="flex-1 w-full px-3 py-2 bg-[#1A1A1B] border border-[#242424] rounded-md text-[#DDDDE6] text-sm"
                  value={vscodePassword}
                  readOnly
                />
                <CopyButton
                  showIcon={true}
                  iconOnly={true}
                  className="border-none bg-none"
                  buttonClassName={buttonVariants({ variant: "outline", size: "sm" , className: "whitespace-nowrap rounded-lg  transition-all ease-in-out duration-200 bg-white/10 border-white/20 border hover:bg-white/20 hover:text-white text-white/40"})}
                  value={vscodePassword}
                  tooltipText="Copy"
                  copiedTooltipText="Copied"
                  // feedbackType="icon"
                  iconProps={{ size: 16 }}
                  onCopy={() => { }}
                />
              </div>
            </div>
          )}
        </div>
        <DialogFooter className="flex-row justify-end w-full gap-4 p-4 md:p-6">
          <DialogClose asChild>
            <Button variant="secondary">Cancel</Button>
          </DialogClose>
          <Button className="flex px-4 py-2 font-semibold text-[16px]"  onClick={onOpenInBrowser}>Open in Browser</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
